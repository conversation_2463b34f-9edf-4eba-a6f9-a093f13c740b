<template>
  <view class="training-container">
    <z-page-navbar>
      <template #right>
        <view class="navbar-actions">
          <view class="navbar-sync-btn" @click="handleManualSync" :class="{ syncing: isSyncing }">
            <i class="fas fa-cloud-download-alt" :class="{ 'fa-spin': isSyncing }"></i>
          </view>
          <view class="navbar-pinyin-toggle" @click="showPinyin = !showPinyin">
            <text>{{ showPinyin ? '隐藏' : '显示' }}拼音</text>
          </view>
        </view>
      </template>
    </z-page-navbar>

    <!-- 单字库 -->
    <view class="word-bank-section" :class="{ collapsed: isWordBankCollapsed }">
      <view class="word-bank-header">
        <view class="word-bank-title-wrapper">
          <view class="word-bank-title">字库</view>
          <view v-if="isWordBankCollapsed && currentWord" class="current-word-display">
            <text>当前：{{ currentWord }}</text>
          </view>
        </view>
        <view class="header-actions">
          <view v-if="currentWord" class="regenerate-btn" @click="handleRegenerateContent">
            <i class="fas fa-sync-alt"></i>
          </view>
          <view class="collapse-icon" @click="isWordBankCollapsed = !isWordBankCollapsed">
            <i class="fas" :class="isWordBankCollapsed ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
          </view>
        </view>
      </view>
      <view v-show="!isWordBankCollapsed" class="word-bank-content">
        <view v-for="(words, category) in wordBank" :key="category" class="category-group">
          <view class="category-name-tag">{{ category }}</view>
          <view v-for="word in words" :key="word" class="history-tag" :class="{ active: currentWord === word }"
            @click="handleTagClick(word)">
            {{ word }}
          </view>
        </view>
      </view>
    </view>

    <!-- 内容展示 -->
    <view class="content-section">
      <!-- 数据加载状态提示 -->
      <view v-if="isLoadingPutonghuaData" class="data-loading-hint">
        <z-loading text="正在加载训练数据..." :font-size="32" padding="40rpx 0" />
      </view>

      <!-- 数据错误提示 -->
      <view v-else-if="putonghuaDataError && !hasCloudData" class="data-error-hint">
        <view class="error-icon">
          <i class="fas fa-exclamation-triangle"></i>
        </view>
        <text class="error-text">{{ putonghuaDataError }}</text>
        <view class="error-actions">
          <view class="retry-btn" @click="retryLoadData">
            <i class="fas fa-redo"></i>
            <text>重试</text>
          </view>
        </view>
      </view>

      <!-- 调试信息 -->
      <view v-if="false" style="background: #f0f0f0; padding: 10px; margin: 10px; font-size: 12px">
        <text>DEBUG INFO:</text><br />
        <text>currentWord: {{ currentWord }}</text><br />
        <text>contentLoading: {{ contentLoading }}</text><br />
        <text>hasCloudData: {{ hasCloudData }}</text><br />
        <text>allPutonghuaData.length: {{ allPutonghuaData.length }}</text><br />
        <text>generatedContent: {{ JSON.stringify(generatedContent) }}</text><br />
        <text>generatedContent.wordsAndPhrases: {{ generatedContent?.wordsAndPhrases?.length || 'undefined'
          }}</text><br />
        <text>generatedContent.sentences: {{ generatedContent?.sentences?.length || 'undefined' }}</text><br />
        <text>generatedContent.paragraph: {{ generatedContent?.paragraph ? 'exists' : 'undefined' }}</text><br />
      </view>

      <z-loading v-if="contentLoading" class="loading-spinner"></z-loading>
      <template v-else>
        <!-- 词语和成语 -->
        <view class="content-block"
          v-if="generatedContent && generatedContent.wordsAndPhrases && generatedContent.wordsAndPhrases.length > 0">
          <view class="content-title">
            <i class="fas fa-book-open"></i>
            <text>词语和成语</text>
          </view>
          <view class="phrase-container">
            <view class="phrase-item" :class="{ selected: selectedWordIndex === index }"
              v-for="(item, index) in generatedContent?.wordsAndPhrases || []" :key="index"
              @click="handleWordClick(index)">
              <view class="text-pinyin-container">
                <view class="character-row">
                  <view class="character-item" v-for="(char, charIndex) in splitTextIntoCharacters(item.text)"
                    :key="charIndex">
                    <view class="pinyin-text" v-if="showPinyin">
                      {{ getPinyinForCharacter(item.pinyin, charIndex, splitTextIntoCharacters(item.text)) }}
                    </view>
                    <view class="chinese-text">{{ char }}</view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 短句 -->
        <view class="content-block"
          v-if="generatedContent && generatedContent.sentences && generatedContent.sentences.length > 0">
          <view class="content-title">
            <i class="fas fa-quote-left"></i>
            <text>对应句子</text>
          </view>
          <view class="sentence-item" v-for="(item, index) in generatedContent?.sentences || []" :key="index">
            <view class="text-pinyin-container">
              <view class="character-row">
                <view class="character-item" v-for="(char, charIndex) in splitTextIntoCharacters(item.text)"
                  :key="charIndex">
                  <view class="pinyin-text" v-if="showPinyin">
                    {{ getPinyinForCharacter(item.pinyin, charIndex, splitTextIntoCharacters(item.text)) }}
                  </view>
                  <view class="chinese-text">{{ char }}</view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 综合段落 -->
        <view class="content-block" v-if="generatedContent && generatedContent.paragraph">
          <view class="content-title">
            <i class="fas fa-paragraph"></i>
            <text>综合段落</text>
          </view>
          <view class="paragraph-item">
            <view class="text-pinyin-container">
              <view class="character-row">
                <view class="character-item"
                  v-for="(char, charIndex) in splitTextIntoCharacters(generatedContent.paragraph.text)"
                  :key="charIndex">
                  <view class="pinyin-text" v-if="showPinyin">
                    {{
                      getPinyinForCharacter(
                        generatedContent.paragraph.pinyin,
                        charIndex,
                        splitTextIntoCharacters(generatedContent.paragraph.text)
                      )
                    }}
                  </view>
                  <view class="chinese-text">{{ char }}</view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 录音练习区域 -->
        <view class="content-block recording-practice-block" v-if="currentWord">
          <view class="content-title">
            <i class="fas fa-microphone"></i>
            <text>录音练习</text>
          </view>

          <!-- 录音播放器 -->
          <view v-if="currentAudioURL" class="audio-player-section">
            <z-audio-player :src="currentAudioURL" :enableTranscription="true" :initial-sentences="currentSentences"
              :showTime="true" :themeColor="'var(--color-primary, #007aff)'" class="practice-audio-player"
              ref="audioPlayerRef" @play="onAudioPlay" @pause="onAudioPause" @ended="onAudioEnded"
              @transcription-start="handleTranscriptionStart" @transcription-end="handleTranscriptionEnd" />
          </view>

          <!-- 录音提示文本 -->
          <view v-else class="recording-hint">
            <text>请录制"{{ currentWord }}"的发音练习</text>
          </view>
        </view>
      </template>
    </view>

    <!-- 录音输入区域 -->
    <view class="recording-input-container" v-if="currentWord">
      <z-message-input v-model="inputMessage" placeholder="点击麦克风开始录音练习..." @send-audio="handleAudioSubmit"
        @upload-progress="handleUploadProgress" @error="handleRecordError" :max-duration="60000" audio-format="mp3"
        cloud-path="speak/" mode="audio-only" />
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed, watch, nextTick, onUnmounted } from 'vue'
import ZMessageInput from '@/components/z-message-input/z-message-input.vue'
import ZAudioPlayer from '@/components/z-audio-player/z-audio-player.vue'
import ZPageNavbar from '@/components/z-page-navbar.vue'
import ZLoading from '@/components/z-loading/index.vue'
// import { generateWordsAndPhrasesFromChar } from '@/api/speak' // 已移除，使用云数据库

// 云对象导入
const speakObj = uniCloud.importObject('speak')

// 内存中存储所有普通话训练数据
let allPutonghuaData = []

// 缓存相关常量
const CACHE_KEY = 'putonghua_training_data'
const CACHE_TIMESTAMP_KEY = 'putonghua_training_data_timestamp'
const CACHE_EXPIRE_TIME = 24 * 60 * 60 * 1000 // 24 小时过期

/**
 * 检查缓存是否有效
 */
const isCacheValid = () => {
  try {
    const timestamp = uni.getStorageSync(CACHE_TIMESTAMP_KEY)
    if (!timestamp) return false

    const now = Date.now()
    const cacheTime = parseInt(timestamp)
    return now - cacheTime < CACHE_EXPIRE_TIME
  } catch (error) {
    return false
  }
}

/**
 * 从本地缓存加载数据
 */
const loadFromCache = () => {
  try {
    const cachedData = uni.getStorageSync(CACHE_KEY)
    if (cachedData && Array.isArray(cachedData)) {
      return cachedData
    }
    return null
  } catch (error) {
    return null
  }
}

/**
 * 保存数据到本地缓存
 */
const saveToCache = (data) => {
  try {
    uni.setStorageSync(CACHE_KEY, data)
    uni.setStorageSync(CACHE_TIMESTAMP_KEY, Date.now().toString())
  } catch (error) {
    // 静默处理缓存保存失败
  }
}

/**
 * 从云数据库加载普通话训练数据
 * 页面初始化时一次性获取所有普通话训练数据
 */
const loadPutonghuaData = async (forceRefresh = false) => {
  isLoadingPutonghuaData.value = true
  putonghuaDataError.value = ''
  isUsingCachedData.value = false

  try {
    const result = await speakObj.getPutonghuaData()

    if (result.code === 0) {
      allPutonghuaData = result.data // 存储到内存
      hasCloudData.value = result.data && result.data.length > 0

      // 保存到本地缓存
      if (result.data && result.data.length > 0) {
        saveToCache(result.data)
      }

      return result.data
    } else {
      throw new Error(result.message || '数据加载失败')
    }
  } catch (error) {
    putonghuaDataError.value = error.message || '数据加载失败'

    // 如果云端获取失败，尝试使用缓存数据
    if (!forceRefresh) {
      const cachedData = loadFromCache()
      if (cachedData && cachedData.length > 0) {
        allPutonghuaData = cachedData
        hasCloudData.value = true
        isUsingCachedData.value = true
        putonghuaDataError.value = '' // 清除错误信息
        return cachedData
      }
    }

    hasCloudData.value = false
    uni.showToast({ title: '数据加载失败', icon: 'none' })
    return []
  } finally {
    isLoadingPutonghuaData.value = false
  }
}

/**
 * 根据汉字从内存中获取训练数据
 */
const getPutonghuaByCharacter = (character) => {
  return allPutonghuaData.find((item) => item.character === character) || null
}

/**
 * 动态生成分类并按分类组织数据
 */
const organizeByCategory = (dataList) => {
  const categories = {}

  // 自动收集所有分类并去重
  dataList.forEach((data) => {
    const category = data.category || '未分类'
    if (!categories[category]) {
      categories[category] = []
    }
    categories[category].push(data.character)
  })

  // 对每个分类内的汉字进行排序
  Object.keys(categories).forEach((category) => {
    categories[category].sort((a, b) => a.localeCompare(b, 'zh-Hans-CN'))
  })

  // 定义分类排序顺序（完善的排序数组，包含所有可能的分类值）
  const categoryOrder = [
    'zh',    // 翘舌音 zh
    'ch',    // 翘舌音 ch
    'sh',    // 翘舌音 sh
    'r',     // 翘舌音 r
    'z',     // 平舌音 z
    'c',     // 平舌音 c
    's',     // 平舌音 s
    'n',     // 鼻音 n
    'l',     // 边音 l
    'an',    // 前鼻音 an
    'en',    // 前鼻音 en
    'in',    // 前鼻音 in
    'un',    // 前鼻音 un
    'ün',    // 前鼻音 ün
    'ang',   // 后鼻音 ang
    'eng',   // 后鼻音 eng
    'ing',   // 后鼻音 ing
    'ong',   // 后鼻音 ong
    '未分类'  // 未分类项放在最后
  ]

  // 按照指定顺序重新组织分类结果
  const sortedCategories = {}

  // 先按顺序添加存在的分类
  categoryOrder.forEach((category) => {
    if (categories[category]) {
      sortedCategories[category] = categories[category]
    }
  })

  // 再添加不在预定义顺序中的其他分类（如果有的话）
  Object.keys(categories).forEach((category) => {
    if (!categoryOrder.includes(category)) {
      sortedCategories[category] = categories[category]
    }
  })

  return sortedCategories
}

/**
 * 随机选择词语和句子（本地操作）
 * 返回最多 5 个不同的词组和对应的句子
 * 支持去重逻辑，避免重复选择已使用的内容
 */
const getRandomContent = (character, excludeUsed = false) => {
  const characterData = getPutonghuaByCharacter(character)
  if (!characterData || !characterData.words || !characterData.words.length) {
    return null
  }

  // 获取已使用的词语 ID 列表
  const usedWordIds =
    excludeUsed && usedContentByCharacter.value[character]
      ? usedContentByCharacter.value[character].usedWordIds || []
      : []

  // 过滤掉已使用的词语
  let availableWords = [...characterData.words]
  if (excludeUsed && usedWordIds.length > 0) {
    availableWords = availableWords.filter((word) => !usedWordIds.includes(word._id || word.text))
  }

  // 如果没有可用词语，重置已使用记录并使用全部词语
  if (availableWords.length === 0) {
    if (usedContentByCharacter.value[character]) {
      usedContentByCharacter.value[character].usedWordIds = []
    }
    availableWords = [...characterData.words]
  }

  // 随机选择最多 5 个不同的词语
  const selectedWords = []
  const maxWords = Math.min(5, availableWords.length)

  for (let i = 0; i < maxWords; i++) {
    const randomIndex = Math.floor(Math.random() * availableWords.length)
    const selectedWord = availableWords.splice(randomIndex, 1)[0]
    selectedWords.push(selectedWord)
  }

  // 为每个词语随机选择一个句子
  const wordsWithSentences = selectedWords.map((word) => {
    const randomSentence =
      word.sentences && word.sentences.length > 0
        ? word.sentences[Math.floor(Math.random() * word.sentences.length)]
        : null

    return {
      word: word,
      sentence: randomSentence,
    }
  })

  // 记录已使用的词语
  if (excludeUsed) {
    if (!usedContentByCharacter.value[character]) {
      usedContentByCharacter.value[character] = { usedWordIds: [] }
    }
    const newUsedIds = selectedWords.map((word) => word._id || word.text)
    usedContentByCharacter.value[character].usedWordIds.push(...newUsedIds)
  }

  return {
    character: characterData.character,
    wordsWithSentences: wordsWithSentences,
  }
}

/**
 * 重试加载数据
 */
const retryLoadData = async () => {
  try {
    const putonghuaData = await loadPutonghuaData()

    if (putonghuaData && putonghuaData.length > 0) {
      // 重新生成字库
      wordBank.value = organizeByCategory(putonghuaData)

      // 如果当前有选中的字符，重新生成内容
      if (currentWord.value) {
        await generateContentForCharacter(currentWord.value)
      }

      uni.showToast({ title: '数据加载成功', icon: 'success' })
    }
  } catch (error) {
    uni.showToast({ title: '重试失败', icon: 'none' })
  }
}

const isWordBankCollapsed = ref(false)

const showPinyin = ref(true)

const wordBank = ref({})

const currentWord = ref('')

// 新增：当前选中的词语状态
const selectedWordIndex = ref(0)

const contentLoading = ref(false)

const mockAudioUrl = ref('https://downsc.chinaz.net/Files/DownLoad/sound1/201906/11582.mp3') // 模拟音频

const generatedContent = ref({
  wordsAndPhrases: [],
  sentences: [],
  paragraph: null,
})

// 新增：存储完整的词语数据（包含句子）
const fullWordsData = ref([])

// 新增：已使用内容的记录（按字符分组）
const usedContentByCharacter = ref({})

// 录音相关状态
const inputMessage = ref('')
const currentAudioURL = ref('')
const isPlaying = ref(false)
const audioPlayerRef = ref(null)

const isTranscribing = ref(false)
const transcriptionText = ref('')
const currentAudioData = ref(null)
const currentSentences = ref(null)

// 数据加载状态管理
const isLoadingPutonghuaData = ref(false)
const putonghuaDataError = ref('')
const hasCloudData = ref(false)
const isUsingCachedData = ref(false)
const isSyncing = ref(false)

// 单字点击逻辑：直接生成新内容
const handleTagClick = async (tag) => {
  currentWord.value = tag
  isWordBankCollapsed.value = true // 选择后自动收起
  uni.setStorageSync('lastSelectedPinyinChar', tag)
  contentLoading.value = true

  // 清空当前内容
  generatedContent.value = { wordsAndPhrases: [], sentences: [], paragraph: null }
  currentAudioURL.value = ''
  transcriptionText.value = ''
  currentSentences.value = null

  try {
    // 切换字符时不使用去重逻辑，重新开始
    await generateContentForCharacter(tag, false)
  } catch (error) {
    uni.showToast({ title: '加载失败', icon: 'none' })
  } finally {
    contentLoading.value = false
  }
}

// 处理词语点击事件
const handleWordClick = (wordIndex) => {
  selectedWordIndex.value = wordIndex
  updateSentencesForSelectedWord()
}

// 更新选中词语的句子
const updateSentencesForSelectedWord = () => {
  if (fullWordsData.value.length === 0 || selectedWordIndex.value >= fullWordsData.value.length) {
    generatedContent.value.sentences = []
    return
  }

  const selectedWord = fullWordsData.value[selectedWordIndex.value]
  if (selectedWord && selectedWord.sentences && selectedWord.sentences.length > 0) {
    // 显示选中词语的所有句子
    generatedContent.value.sentences = selectedWord.sentences.map((sentence) => ({
      text: sentence.text,
      pinyin: sentence.pinyin,
    }))
  } else {
    generatedContent.value.sentences = []
  }
}

// 重新生成内容的函数
const handleRegenerateContent = async () => {
  if (!currentWord.value) return

  contentLoading.value = true

  try {
    // 重新生成时使用去重逻辑，避免重复内容
    await generateContentForCharacter(currentWord.value, true)
    uni.showToast({ title: '重新生成成功', icon: 'success' })
  } catch (error) {
    uni.showToast({ title: '重新生成失败', icon: 'none' })
  } finally {
    contentLoading.value = false
  }
}

// 为指定字符生成内容的通用函数
const generateContentForCharacter = async (character, excludeUsed = false) => {
  // 清空当前内容
  generatedContent.value = { wordsAndPhrases: [], sentences: [], paragraph: null }
  currentAudioURL.value = ''
  transcriptionText.value = ''
  currentSentences.value = null

  // 优先从云数据库获取数据
  const randomContent = getRandomContent(character, excludeUsed)

  if (randomContent) {
    // 构建词语和短语数据（最多 5 个）
    const wordsAndPhrases = []
    const fullWords = []
    if (randomContent.wordsWithSentences && randomContent.wordsWithSentences.length > 0) {
      randomContent.wordsWithSentences.forEach((item) => {
        if (item.word) {
          wordsAndPhrases.push({
            text: item.word.text,
            pinyin: item.word.pinyin,
          })
          // 存储完整的词语数据（包含所有句子）
          fullWords.push(item.word)
        }
      })
    }

    // 设置生成的内容
    generatedContent.value.wordsAndPhrases = wordsAndPhrases
    generatedContent.value.paragraph = null // 云数据库暂不支持段落

    // 存储完整的词语数据
    fullWordsData.value = fullWords

    // 默认选中第一个词语并显示其句子
    selectedWordIndex.value = 0
    updateSentencesForSelectedWord()
  } else {
    // 显示无数据提示
    uni.showToast({
      title: '该字符暂无训练数据',
      icon: 'none',
      duration: 2000,
    })

    // 设置空内容
    generatedContent.value.wordsAndPhrases = []
    generatedContent.value.sentences = []
    generatedContent.value.paragraph = null
    fullWordsData.value = []
    selectedWordIndex.value = 0
  }

  // 加载已存在的录音
  await loadExistingRecording(character)
}

// 清空录音记录（不再从历史记录加载）
const loadExistingRecording = async (character) => {
  // 每次都清空录音状态，不再从历史记录加载
  currentAudioURL.value = ''
  transcriptionText.value = ''
  currentSentences.value = null
}

/**
 * 手动同步数据
 */
const handleManualSync = async () => {
  if (isSyncing.value) return

  isSyncing.value = true

  try {
    // 强制从云端获取最新数据
    const putonghuaData = await loadPutonghuaData(true)

    if (putonghuaData && putonghuaData.length > 0) {
      // 重新生成字库
      wordBank.value = organizeByCategory(putonghuaData)

      // 如果当前有选中的字符，重新生成内容
      if (currentWord.value) {
        await generateContentForCharacter(currentWord.value)
      }

      isUsingCachedData.value = false
      uni.showToast({ title: '同步成功', icon: 'success' })
    } else {
      uni.showToast({ title: '同步失败', icon: 'none' })
    }
  } catch (error) {
    uni.showToast({ title: '同步失败', icon: 'none' })
  } finally {
    isSyncing.value = false
  }
}

/**
 * 初始化数据加载
 */
const initializeData = async () => {
  // 1. 优先检查缓存
  if (isCacheValid()) {
    const cachedData = loadFromCache()
    if (cachedData && cachedData.length > 0) {
      allPutonghuaData = cachedData
      hasCloudData.value = true
      isUsingCachedData.value = true

      // 使用缓存数据生成字库
      wordBank.value = organizeByCategory(cachedData)
      return cachedData
    }
  }

  // 2. 缓存无效或无缓存，从云端获取
  const putonghuaData = await loadPutonghuaData()

  if (putonghuaData && putonghuaData.length > 0) {
    wordBank.value = organizeByCategory(putonghuaData)
  } else {
    wordBank.value = {}
    if (!isUsingCachedData.value) {
      uni.showToast({
        title: '暂无训练数据，请联系管理员添加',
        icon: 'none',
        duration: 3000,
      })
    }
  }

  return putonghuaData
}

onMounted(async () => {
  try {
    // 1. 初始化数据加载
    const putonghuaData = await initializeData()

    // 2. 按字库逻辑处理初始选择
    if (Object.keys(wordBank.value).length > 0) {
      const lastSelectedWord = uni.getStorageSync('lastSelectedPinyinChar')

      const allWords = Object.values(wordBank.value).flat()
      if (lastSelectedWord && allWords.includes(lastSelectedWord)) {
        handleTagClick(lastSelectedWord)
      } else {
        const firstCategory = Object.keys(wordBank.value)[0]
        if (wordBank.value[firstCategory]?.length > 0) {
          handleTagClick(wordBank.value[firstCategory][0])
        }
      }
    }
  } catch (error) {
    // 静默处理初始化错误
  }
})

// 录音相关方法
const handleAudioSubmit = async (audioData) => {
  if (audioData && audioData.tempFileURL) {
    currentAudioURL.value = audioData.tempFileURL
    currentAudioData.value = audioData // 暂存音频数据，用于转写后保存

    // 使用 nextTick 确保 DOM 更新后，player 组件的 ref 可用
    nextTick(() => {
      if (audioPlayerRef.value) {
        // 显式调用子组件的转写方法
        audioPlayerRef.value.transcribe()
      }
    })
  }
}

const handleUploadProgress = (progress) => {
  // 静默处理上传进度
}

const handleRecordError = (error) => {
  uni.showToast({
    title: '录音出错，请重试',
    icon: 'none',
    duration: 3000,
  })
}

// 转写事件处理
const handleTranscriptionStart = () => {
  isTranscribing.value = true
  transcriptionText.value = ''
}

const handleTranscriptionEnd = async (result) => {
  isTranscribing.value = false

  if (result.success && result.transcript) {
    transcriptionText.value = result.transcript
    currentSentences.value = result.sentences
    uni.showToast({
      title: '识别成功',
      icon: 'success',
    })
    // 清理临时数据
    if (currentAudioData.value) {
      currentAudioData.value = null // 清理临时数据
    }
  } else {
    uni.showToast({
      title: '识别失败，请重试',
      icon: 'none',
    })
    // 即使转写失败，也清理临时数据
    if (currentAudioData.value) {
      transcriptionText.value = '' // 确保转写文本为空
      currentSentences.value = null
      currentAudioData.value = null // 清理临时数据
    }
  }
}

// 音频播放器事件处理
const onAudioPlay = () => {
  isPlaying.value = true
}

const onAudioPause = () => {
  isPlaying.value = false
}

const onAudioEnded = () => {
  isPlaying.value = false
}

const handleReset = () => {
  currentWord.value = ''
  generatedContent.value = { wordsAndPhrases: [], sentences: [], paragraph: null }
  wordBank.value = {}

  // 重置新增的状态
  fullWordsData.value = []
  selectedWordIndex.value = 0
  usedContentByCharacter.value = {} // 清理已使用内容记录

  // 清理录音相关状态
  currentAudioURL.value = ''
  inputMessage.value = ''

  transcriptionText.value = ''
  currentSentences.value = null
  isTranscribing.value = false
  currentAudioData.value = null

  // 停止任何正在播放的音频
  if (audioPlayerRef.value) {
    audioPlayerRef.value.pause()
  }
  isPlaying.value = false
}

// 组件卸载时清理资源
onUnmounted(() => {
  if (currentAudioURL.value && currentAudioURL.value.startsWith('blob:')) {
    URL.revokeObjectURL(currentAudioURL.value)
  }

  // 停止音频播放
  if (audioPlayerRef.value) {
    audioPlayerRef.value.pause()
  }
})

// 文字和拼音对齐处理函数
const splitTextIntoCharacters = (text) => {
  if (!text) {
    return []
  }
  // 将文本分割成单个字符，包括汉字、标点符号等
  const result = text.split('')
  return result
}

// 判断字符是否为中文汉字
const isChineseCharacter = (char) => {
  // 匹配中文汉字的 Unicode 范围
  return /[\u4e00-\u9fff]/.test(char)
}

const getPinyinForCharacter = (pinyinString, charIndex, allCharacters) => {
  if (!pinyinString) {
    return ''
  }

  // 如果当前字符不是汉字，返回空字符串
  if (allCharacters && allCharacters[charIndex] && !isChineseCharacter(allCharacters[charIndex])) {
    return ''
  }

  // 将拼音字符串按空格分割
  const pinyinArray = pinyinString.trim().split(/\s+/)

  // 计算当前字符在汉字序列中的索引（排除标点符号）
  let chineseCharIndex = 0
  if (allCharacters) {
    for (let i = 0; i < charIndex; i++) {
      if (isChineseCharacter(allCharacters[i])) {
        chineseCharIndex++
      }
    }
  } else {
    chineseCharIndex = charIndex
  }

  // 返回对应位置的拼音，如果超出范围则返回空字符串
  const result = pinyinArray[chineseCharIndex] || ''

  return result
}
</script>

<style lang="scss" scoped>
.training-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.navbar-actions {
  display: flex;
  align-items: center;
  gap: 15px;
  padding-right: 5px;

  .action-btn {
    font-size: 18px;
    color: var(--color-text-secondary);
    cursor: pointer;
  }
}

.navbar-sync-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background-color: var(--color-primary-light-9);
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }

  &.syncing {
    background-color: var(--color-primary-light-7);
  }

  i {
    font-size: 14px;
    color: var(--color-primary);
    transition: transform 0.3s ease;
  }
}

.navbar-pinyin-toggle {
  font-size: 14px;
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
  padding: 4px 8px;
  border-radius: var(--rounded-md);
  cursor: pointer;
  transition: all 0.2s;
}

.word-bank-section {
  background-color: #fff;
  margin: 15px;
  padding: 15px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;

  .word-bank-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .word-bank-title-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .current-word-display {
    background-color: var(--color-primary-light-9);
    color: var(--color-primary);
    padding: 3px 8px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 8px;

    .regenerate-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 28px;
      height: 28px;
      border-radius: 6px;
      background-color: var(--color-primary-light-9);
      cursor: pointer;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
      }

      i {
        font-size: 12px;
        color: var(--color-primary);
      }
    }

    .collapse-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 28px;
      height: 28px;
      cursor: pointer;
      transition: all 0.3s ease;

      i {
        font-size: 14px;
        color: #909399;
        transition: transform 0.3s ease;
      }
    }
  }

  &.collapsed .word-bank-content {
    display: none;
  }

  &:not(.collapsed) .word-bank-header {
    margin-bottom: 15px;
  }

  .word-bank-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 0;
  }
}

.word-bank-content {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;
}

.category-group {
  display: inline-flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;
  background-color: #f8f9fa;
  padding: 5px 10px;
  border-radius: 8px;
}

.category-name-tag {
  background-color: #e9ecef;
  color: #495057;
  border-radius: 6px;
  padding: 8px 10px;
  font-size: 14px;
  font-weight: 500;
}

.history-tag {
  background-color: var(--color-primary-light-9);
  color: var(--color-primary);
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &.active {
    background-color: var(--color-primary);
    color: #fff;
    font-weight: bold;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(41, 121, 255, 0.4);
  }
}

.add-tag {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 38px;
  height: 38px;
  border-radius: 8px;
  background-color: #f5f7fa;
  border: 1px dashed #dcdfe6;
  cursor: pointer;
  transition: all 0.2s;

  i {
    font-size: 16px;
    color: #909399;
  }



  i {
    color: var(--color-primary);
  }
}

.content-section {
  flex: 1;
  overflow-y: auto;
  padding: 0 15px 100px 15px; // 增加底部内边距为录音输入区域留空间
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 200px;
}

// 数据加载状态样式
.data-loading-hint {
  width: 100%;
  text-align: center;
  padding: 40px 20px;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.data-error-hint {
  width: 100%;
  text-align: center;
  padding: 40px 20px;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border-left: 4px solid var(--color-danger, #f56c6c);

  .error-icon {
    margin-bottom: 15px;

    i {
      font-size: 32px;
      color: var(--color-danger, #f56c6c);
    }
  }

  .error-text {
    display: block;
    color: var(--color-text-secondary);
    font-size: 14px;
    margin-bottom: 20px;
    line-height: 1.5;
  }

  .error-actions {
    display: flex;
    justify-content: center;
  }

  .retry-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background-color: var(--color-primary);
    color: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 14px;

    &:active {
      transform: translateY(0);
    }

    i {
      font-size: 12px;
    }
  }
}

.content-block {
  background-color: #fff;
  border-radius: 8px;
  padding: 12px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    width: 100%;
  
    .content-title {
      display: flex;
      align-items: center;
      font-size: 15px;
      font-weight: bold;
      color: #333;
      margin-bottom: 10px;
  
      i {
        color: var(--color-primary);
        margin-right: 6px;
      }
    }
  
    // 文字和拼音对齐样式
    .text-pinyin-container {
      display: inline-block;
      width: 100%;
    }
  
    .character-row {
      display: flex;
      flex-wrap: wrap;
      align-items: flex-end;
      gap: 1px;
    }
  
    .character-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-end;
      min-width: 20px;
      margin: 0 0.5px;
      position: relative;
    }
  
    .pinyin-text {
      font-size: 10px;
      color: #888;
      line-height: 1.1;
      text-align: center;
      white-space: nowrap;
      font-weight: normal;
      margin-bottom: 1px;
      min-height: 11px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  
    .chinese-text {
      font-size: 16px;
      line-height: 1.3;
    color: #333;
    text-align: center;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 22px;
  }
}

.phrase-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: flex-start;
}

.phrase-item,
.sentence-item {
  background-color: #f5f7fa;
  border-radius: 6px;
  padding: 10px 12px;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-block;
  margin: 3px;

  &.selected {
    background-color: var(--color-primary);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(41, 121, 255, 0.3);

    .pinyin-text {
      color: rgba(255, 255, 255, 0.8);
    }

    .chinese-text {
      color: white;
      font-weight: 600;
    }


  }
}

.sentence-item {
  display: block;
  width: 100%;
  margin: 6px 0;

  .character-row {
    justify-content: flex-start;
  }
}

.paragraph-item {
  display: block;
  width: 100%;
  margin: 6px 0;
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 12px 16px;
  border-left: 4px solid var(--color-primary);

  .character-row {
    justify-content: flex-start;
    line-height: 1.6;
  }

  .text-pinyin-container {
    width: 100%;
  }
}
// 录音练习区域样式
.recording-practice-block {
  margin-bottom: 20px;
  border: 2px solid var(--color-primary-light-8);
  background: linear-gradient(135deg, #f8fbff 0%, #f0f7ff 100%);

  .content-title {
    color: var(--color-primary);
    font-weight: 600;

    i {
      color: var(--color-primary);
    }
  }
}

.audio-player-section {
  margin-top: 15px;
  padding: 15px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  border: 1px solid var(--color-primary-light-7);
}

.practice-audio-player {
  width: 100%;
}

.recording-hint {
  text-align: center;
  padding: 20px;
  color: var(--color-text-secondary);
  font-size: 15px;
  font-style: italic;

  text {
    color: var(--color-primary);
    font-weight: 500;
  }
}

// 录音输入区域样式
.recording-input-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--color-white, #fff);
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.06);
  z-index: 100;
  border-top: 1px solid var(--color-border, #e9e9e9);
}
.audio-player-placeholder {
  padding: 0 15px 10px;
}

.recording-section {
  border-top: 1px solid #e0e0e0;
}
</style>
